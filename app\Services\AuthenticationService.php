<?php

namespace App\Services;

use App\Models\User;
use App\Models\Applicant;
use App\Models\Recommender;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Firebase\JWT\JWT;
use Firebase\JWT\KEY;

/**
 * 統一認證服務類
 */
class AuthenticationService
{
    /**
     * 申請人 JWT 登入處理
     *
     * @param array $jwtPayload JWT 解碼後的資料
     * @param string $clientIp 客戶端 IP
     * @return array 登入結果
     */
    public function loginApplicantWithJWT(array $jwtPayload, string $clientIp): array
    {
        try {

            $external_uid = $jwtPayload['stu_idno']; // todo 須加密
            $stu_year = $jwtPayload['stu_year'];
            $exam_id = $jwtPayload['exam_id'];

            // 1. 根據考試資訊查找現有申請人記錄
            $applicant = Applicant::findByExamInfo($exam_id, $stu_year, $external_uid);

            if (!$applicant) {
                // 2. 初次登入，向原系統查詢使用者資訊和報名資料
                $userData = $this->fetchUserDataFromExternalSystem($external_uid, $stu_year, $exam_id);

                if (!$userData) {
                    throw new \Exception('無法從原系統取得使用者資訊');
                }

                // 4. 建立新使用者和申請人記錄
                $user = $this->createUserFromExternalData($userData);
                $applicant = $this->createApplicantRecord($user, $external_uid, $stu_year, $exam_id, $userData);
            } else {
                // 5. 現有使用者，取得關聯的 User 記錄
                $user = $applicant->user;
                if (!$user) {
                    throw new \Exception('申請人記錄損壞，請聯繫系統管理員');
                }
            }

            // 6. 執行登入並設定 session
            $this->performLogin($user, [
                'applicant_id' => $applicant->id,
                'external_uid' => $external_uid,
                'exam_year' => $stu_year,
                'exam_id' => $exam_id,
                'login_method' => 'jwt',
            ]);

            // 7. 記錄登入日誌
            Log::info('申請人 JWT 登入成功', [
                'external_uid' => $external_uid,
                'exam_year' => $stu_year,
                'exam_id' => $exam_id,
                'user_id' => $user->id,
                'applicant_id' => $applicant->id,
                'ip' => $clientIp,
            ]);

            return [
                'success' => true,
                'message' => '登入成功',
                'user' => $user,
                'applicant' => $applicant,
            ];
        } catch (\Exception $e) {
            Log::error('申請人 JWT 登入失敗', [
                'jwt_payload' => $jwtPayload,
                'error' => $e->getMessage(),
                'ip' => $clientIp,
            ]);

            return [
                'success' => false,
                'message' => '登入失敗：' . $e->getMessage(),
            ];
        }
    }




    /**
     * 推薦人 Token 登入處理
     * 
     * @param string $token 登入 Token
     * @param string $clientIp 客戶端 IP
     * @return array 登入結果
     */
    public function loginRecommenderByToken(string $token, string $clientIp): array
    {
        try {
            // 1. 驗證 Token 並查找推薦人
            $recommender = Recommender::where('login_token', $token)
                ->where(function ($query) {
                    $query->whereNull('token_expires_at')
                        ->orWhere('token_expires_at', '>', now());
                })
                ->first();

            if (!$recommender) {
                return [
                    'success' => false,
                    'message' => 'Token 無效或已過期',
                ];
            }

            // 2. 查找或建立使用者帳號
            $user = $this->findOrCreateUser([
                'email' => $recommender->email,
                'name' => $recommender->name,
                'role' => 'recommender',
            ]);

            // 3. 確保推薦人與使用者關聯
            if (!$recommender->user_id) {
                $recommender->update(['user_id' => $user->id]);
            }

            // 4. 更新最後登入時間
            $recommender->updateLastLogin();

            // 5. 執行登入並設定 session
            $this->performLogin($user, [
                'recommender_id' => $recommender->id,
                'login_token' => $token,
            ]);

            // 6. 記錄登入日誌
            Log::info('推薦人登入成功', [
                'recommender_id' => $recommender->id,
                'user_id' => $user->id,
                'token' => $token,
                'ip' => $clientIp,
            ]);

            return [
                'success' => true,
                'user' => $user,
                'recommender' => $recommender,
                'message' => '登入成功',
            ];
        } catch (\Exception $e) {
            Log::error('推薦人登入失敗', [
                'token' => $token,
                'error' => $e->getMessage(),
                'ip' => $clientIp,
            ]);

            return [
                'success' => false,
                'message' => '登入失敗：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 統一登出處理
     * 
     * @param string $userType 使用者類型 (applicant|recommender|admin)
     * @return void
     */
    public function logout(string $userType = 'general'): void
    {
        // 根據使用者類型清除特定的 session 資料
        switch ($userType) {
            case 'applicant':
                Session::forget(['applicant_id', 'external_uid']);
                break;
            case 'recommender':
                Session::forget(['recommender_id', 'login_token']);
                break;
            case 'admin':
            default:
                // 一般登出，清除所有相關 session
                Session::forget(['applicant_id', 'external_uid', 'recommender_id', 'login_token']);
                break;
        }

        // 執行 Laravel 標準登出流程
        Auth::logout();
        request()->session()->invalidate();
        request()->session()->regenerateToken();

        Log::info('使用者登出', [
            'user_type' => $userType,
            'ip' => request()->ip(),
        ]);
    }

    /**
     * 查找或建立使用者帳號
     * 
     * @param array $userData 使用者資料
     * @return User
     */
    private function findOrCreateUser(array $userData): User
    {
        $user = User::where('email', $userData['email'])->first();

        if (!$user) {
            $user = User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'role' => $userData['role'],
            ]);

            Log::info('建立新使用者帳號', [
                'user_id' => $user->id,
                'email' => $userData['email'],
                'role' => $userData['role'],
            ]);
        }

        return $user;
    }

    /**
     * 執行登入並設定 session
     * 
     * @param User $user 使用者物件
     * @param array $sessionData 額外的 session 資料
     * @return void
     */
    private function performLogin(User $user, array $sessionData = []): void
    {
        // 執行 Laravel 認證登入
        Auth::login($user);

        // 重新生成 session ID 以防止 session fixation 攻擊
        // 檢查是否有 session 可用
        if (request()->hasSession()) {
            request()->session()->regenerate();
        }

        // 設定額外的 session 資料
        foreach ($sessionData as $key => $value) {
            Session::put($key, $value);
        }

        Log::debug('登入 session 設定完成', [
            'user_id' => $user->id,
            'session_data' => array_keys($sessionData),
            'has_session' => request()->hasSession(),
        ]);
    }

    /**
     * 從外部系統取得使用者資訊
     *
     * @param string $stu_idno 學號
     * @param string $stu_year 學年度
     * @param string $exam_id 考試ID
     * @return array|null 使用者資料
     */
    private function fetchUserDataFromExternalSystem(string $stu_idno, string $stu_year, string $exam_id): ?array
    {
        try {
            // 生成加密查詢條件
            $encryptedQuery = $this->generateEncryptedQuery($stu_idno, $stu_year, $exam_id);

            $externalApiUrl = config('app.external_api_url', 'http://localhost:18002');
            $url = "{$externalApiUrl}/index.php/api/rec-letter/user-info/{$encryptedQuery}";

            Log::info('查詢外部系統使用者資訊', [
                'stu_idno' => $stu_idno,
                'stu_year' => $stu_year,
                'exam_id' => $exam_id,
                'url' => $url,
            ]);

            $response = \Illuminate\Support\Facades\Http::timeout(10)->get($url);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['valid']) && $data['valid'] === true && isset($data['user_info'])) {
                    Log::info('外部系統使用者資訊取得成功', [
                        'stu_idno' => $stu_idno,
                        'user_name' => $data['user_info']['stu_name'] ?? 'unknown',
                    ]);

                    return $data['user_info'];
                } else {
                    Log::warning('外部系統使用者資訊回應格式錯誤', [
                        'stu_idno' => $stu_idno,
                        'response' => $data,
                    ]);

                    return null;
                }
            } else {
                Log::error('外部系統使用者資訊 API 呼叫失敗', [
                    'stu_idno' => $stu_idno,
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);

                return null;
            }
        } catch (\Exception $e) {
            Log::error('外部系統使用者資訊 API 呼叫異常', [
                'stu_idno' => $stu_idno,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * 生成加密查詢條件
     *
     * @param string $stu_idno 學號
     * @param string $stu_year 學年度
     * @param string $exam_id 考試ID
     * @return string 加密後的查詢條件
     */
    private function generateEncryptedQuery($stu_idno, $stu_year, $exam_id)
    {
        $payload = [
            'stu_idno' => $stu_idno,
            'stu_year' => $stu_year,
            'exam_id' => $exam_id,
            'iat' => time(),              // issued at
            'exp' => time() + 60 * 10,    // 過期時間：10 分鐘
        ];

        return JWT::encode($payload, config('app.jwt_secret'), 'HS256');
    }

    /**
     * 從外部系統查詢申請人的報名資料
     *
     * @param string $stu_idno 學號
     * @param string $stu_year 學年度
     * @param string $exam_id 考試ID
     * @return array|null 報名資料陣列
     */
    public function fetchApplicationsFromExternalSystem(string $stu_idno, string $stu_year, string $exam_id): ?array
    {
        try {
            // 生成查詢 Token (使用相同的加密邏輯)
            $encryptedQuery = $this->generateEncryptedQuery($stu_idno, $stu_year, $exam_id);

            $externalApiUrl = config('app.external_api_url', 'http://localhost:18002');
            $url = "{$externalApiUrl}/index.php/api/rec-letter/applications/{$encryptedQuery}";

            Log::info('查詢外部系統報名資料', [
                'stu_idno' => $stu_idno,
                'stu_year' => $stu_year,
                'exam_id' => $exam_id,
                'url' => $url,
            ]);

            $response = \Illuminate\Support\Facades\Http::timeout(10)->get($url);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['valid']) && $data['valid'] === true && isset($data['applications'])) {
                    Log::info('外部系統報名資料取得成功', [
                        'stu_idno' => $stu_idno,
                        'applications_count' => count($data['applications']),
                    ]);

                    return $data['applications'];
                } else {
                    Log::warning('外部系統報名資料無效', [
                        'stu_idno' => $stu_idno,
                        'response' => $data,
                    ]);
                }
            } else {
                Log::error('外部系統報名資料 API 呼叫失敗', [
                    'stu_idno' => $stu_idno,
                    'status' => $response->status(),
                    'body' => substr($response->body(), 0, 500),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('外部系統報名資料 API 呼叫異常', [
                'stu_idno' => $stu_idno,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    /**
     * 從外部資料建立使用者
     *
     * @param array $userData 外部使用者資料
     * @return User 建立的使用者
     */
    private function createUserFromExternalData(array $userData): User
    {
        $user = User::create([
            'name' => $userData['stu_name'],
            'email' => $userData['stu_e_mail'],
            'role' => 'applicant',
        ]);

        Log::info('從外部資料建立新使用者', [
            'user_id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
        ]);

        return $user;
    }

    /**
     * 建立申請人記錄
     *
     * @param User $user 使用者物件
     * @param string $stu_idno 學號
     * @param string $stu_year 學年度
     * @param string $exam_id 考試ID
     * @param array $userData 使用者資料
     * @return Applicant 建立的申請人記錄
     */
    private function createApplicantRecord(User $user, string $external_uid, string $stu_year, string $exam_id, array $userData): Applicant
    {
        $applicant = Applicant::create([
            'user_id' => $user->id,
            'external_uid' => $external_uid,
            'exam_year' => $stu_year,
            'exam_id' => $exam_id,
            'phone' => $userData['stu_cell_phone'] ?? null,
        ]);

        Log::info('建立新申請人記錄', [
            'user_id' => $user->id,
            'applicant_id' => $applicant->id,
            'external_uid' => $external_uid,
            'exam_year' => $stu_year,
            'exam_id' => $exam_id,
        ]);

        return $applicant;
    }
}
