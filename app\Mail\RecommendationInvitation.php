<?php

namespace App\Mail;

use App\Models\RecommendationLetter;
use App\Models\Recommender;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class RecommendationInvitation extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 60;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public RecommendationLetter $recommendationLetter,
        public Recommender $recommender,
        public string $loginUrl
    ) {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: '推薦函邀請 - ' . $this->recommendationLetter->department_name,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.recommendation-invitation',
            with: [
                'recommendationLetter' => $this->recommendationLetter,
                'recommender' => $this->recommender,
                'loginUrl' => $this->loginUrl,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
