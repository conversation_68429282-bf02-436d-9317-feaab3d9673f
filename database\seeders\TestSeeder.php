<?php

namespace Database\Seeders;

use App\Models\QuestionnaireTemplate;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

/**
 * 測試資料植入器
 * 
 * 模擬真實的第三方登入流程建立測試資料
 */
class TestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 開始植入測試資料...');

        // 1. 建立管理員帳號
        $this->createAdminUser();


        // 2. 建立問卷模板
        $this->createQuestionnaireTemplates();

        $this->command->info('✅ Test data seeded successfully!');
        $this->command->info('📧 Admin: <EMAIL> (password: password)');
    }

    /**
     * 建立管理員帳號
     */
    private function createAdminUser(): void
    {
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'role' => 'admin',
            ]
        );

        $this->command->info('👑 管理員帳號已建立: ' . $adminUser->email);
    }

    /**
     * 建立問卷模板
     */
    private function createQuestionnaireTemplates(): void
    {
        $templates = [
            [
                'template_name' => '一般推薦函問卷',
                'description' => '適用於一般學術推薦的標準問卷',
                'department_name' => '通用',
                'program_type' => '一般',
                'is_active' => true,
                'questions' => [
                    [
                        'id' => 'q1_relationship',
                        'type' => 'text',
                        'label' => '請問您與推薦人的關係',
                        'required' => true,
                    ],
                    [
                        'id' => 'q2_known_for',
                        'type' => 'text',
                        'label' => '請問您與推薦人認識多久了',
                        'required' => true,
                    ],



                    // 8 題 radio 題型（Likert scale）
                    [
                        'id' => 'q3_communication',
                        'type' => 'radio',
                        'label' => '申請人具備良好的溝通能力',
                        'options' => ['非常不同意', '不同意', '普通', '同意', '非常同意'],
                        'required' => true,
                    ],
                    [
                        'id' => 'q4_teamwork',
                        'type' => 'radio',
                        'label' => '申請人能與他人良好合作',
                        'options' => ['非常不同意', '不同意', '普通', '同意', '非常同意'],
                        'required' => true,
                    ],
                    [
                        'id' => 'q5_problem_solving',
                        'type' => 'radio',
                        'label' => '申請人具有解決問題的能力',
                        'options' => ['非常不同意', '不同意', '普通', '同意', '非常同意'],
                        'required' => true,
                    ],
                    [
                        'id' => 'q6_leadership',
                        'type' => 'radio',
                        'label' => '申請人展現領導潛力',
                        'options' => ['非常不同意', '不同意', '普通', '同意', '非常同意'],
                        'required' => true,
                    ],
                    [
                        'id' => 'q7_responsibility',
                        'type' => 'radio',
                        'label' => '申請人對工作/學業負責任',
                        'options' => ['非常不同意', '不同意', '普通', '同意', '非常同意'],
                        'required' => true,
                    ],
                    [
                        'id' => 'q8_initiative',
                        'type' => 'radio',
                        'label' => '申請人主動積極',
                        'options' => ['非常不同意', '不同意', '普通', '同意', '非常同意'],
                        'required' => true,
                    ],
                    [
                        'id' => 'q9_adaptability',
                        'type' => 'radio',
                        'label' => '申請人能快速適應新環境',
                        'options' => ['非常不同意', '不同意', '普通', '同意', '非常同意'],
                        'required' => true,
                    ],
                    [
                        'id' => 'q10_time_management',
                        'type' => 'radio',
                        'label' => '申請人具備良好的時間管理能力',
                        'options' => ['非常不同意', '不同意', '普通', '同意', '非常同意'],
                        'required' => true,
                    ],

                    // 2 題 textarea 題型
                    [
                        'id' => 'q11_strengths',
                        'type' => 'textarea',
                        'label' => '請描述申請人最突出的優點或貢獻',
                        'required' => false,
                        'max_length' => 1000,
                    ],
                    [
                        'id' => 'q12_improvements',
                        'type' => 'textarea',
                        'label' => '請提供您認為申請人可改善之處',
                        'required' => false,
                        'max_length' => 1000,
                    ],

                    // 1 題 checkbox 題型（Likert scale）
                    [
                        'id' => 'q13_recommendation',
                        'type' => 'radio',
                        'label' => '請問您推薦此申請人的程度',
                        'options' => ['非常不推薦', '不推薦', '普通', '推薦', '非常推薦'],
                        'required' => true,
                    ],
                ],
            ]
        ];

        foreach ($templates as $templateData) {
            $template = QuestionnaireTemplate::firstOrCreate(
                [
                    'template_name' => $templateData['template_name'],
                    'department_name' => $templateData['department_name'],
                    'program_type' => $templateData['program_type']
                ],
                [
                    'description' => $templateData['description'],
                    'is_active' => $templateData['is_active'],
                    'questions' => $templateData['questions']
                ]
            );

            $this->command->info('📝 問卷模板已建立: ' . $template->template_name);
        }
    }
}
