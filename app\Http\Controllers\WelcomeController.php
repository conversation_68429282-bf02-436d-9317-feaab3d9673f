<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

/**
 * 歡迎頁面控制器
 * 處理首頁顯示和招生資訊取得
 */
class WelcomeController extends Controller
{
    /**
     * 顯示歡迎頁面
     * 
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        // 取得招生資訊
        $admissionInfo = $this->getAdmissionInfo();

        return Inertia::render('welcome', [
            'admission_info' => $admissionInfo,
        ]);
    }

    /**
     * 從外部 API 取得招生資訊
     * 
     * @return array
     */
    private function getAdmissionInfo(): array
    {
        try {
            $externalApiUrl = config('app.external_api_url', env('EXTERNAL_API_URL', 'http://localhost:18002'));
            $apiUrl = "{$externalApiUrl}/index.php/api/rec-letter/admission-info";

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                Log::error('招生資訊 API 呼叫失敗', [
                    'url' => $apiUrl,
                    'error' => $error
                ]);
                return [];
            }

            if ($httpCode !== 200) {
                Log::warning('招生資訊 API 回應異常', [
                    'url' => $apiUrl,
                    'http_code' => $httpCode,
                    'response' => $response
                ]);
                return [];
            }

            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('招生資訊 API 回應 JSON 解析失敗', [
                    'url' => $apiUrl,
                    'json_error' => json_last_error_msg(),
                    'response' => $response
                ]);
                return [];
            }

            // 檢查 API 回應格式
            if (!isset($data['valid']) || !$data['valid']) {
                Log::warning('招生資訊 API 回應無效', [
                    'response' => $data
                ]);
                return [];
            }

            if (!isset($data['admission_info']) || !is_array($data['admission_info'])) {
                Log::warning('招生資訊 API 回應格式異常', [
                    'response' => $data
                ]);
                return [];
            }

            Log::info('成功取得招生資訊', [
                'admission_count' => count($data['admission_info'])
            ]);

            return $data['admission_info'];
        } catch (\Exception $e) {
            Log::error('取得招生資訊異常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }
}
