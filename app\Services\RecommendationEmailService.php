<?php

namespace App\Services;

use App\Mail\RecommendationInvitation;
use App\Mail\RecommendationReminder;
use App\Models\EmailLog;
use App\Models\RecommendationLetter;
use App\Models\Recommender;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

/**
 * 推薦函郵件服務類
 * 支援 Laravel Mail 和外部 API 兩種發送方式
 */
class RecommendationEmailService
{
    /**
     * 外部郵件服務實例
     */
    private ExternalMailService $externalMailService;

    /**
     * 是否使用外部 API 發送郵件
     */
    private bool $useExternalApi;

    public function __construct()
    {
        $this->externalMailService = new ExternalMailService();
        $this->useExternalApi = config('mail.external_api.enabled', false);
    }
    /**
     * Send invitation email to recommender.
     */
    public function sendInvitation(RecommendationLetter $recommendationLetter): bool
    {
        try {
            // Get recommender record (should already exist from RecommenderService)
            $recommender = Recommender::where('email', $recommendationLetter->recommender_email)->first();

            if (!$recommender) {
                Log::error('Recommender not found when sending invitation', [
                    'recommendation_id' => $recommendationLetter->id,
                    'recommender_email' => $recommendationLetter->recommender_email
                ]);
                return false;
            }

            // Generate login URL with token
            $loginUrl = $this->generateLoginUrl($recommender);

            // Create email log
            $emailLog = EmailLog::create([
                'recommendation_letter_id' => $recommendationLetter->id,
                'to_email' => $recommendationLetter->recommender_email,
                'subject' => '推薦函邀請 - ' . $recommendationLetter->department_name,
                'template_name' => 'recommendation-invitation',
                'email_type' => 'invitation',
                'status' => 'pending',
            ]);

            // Send email - 支援外部 API 和 Laravel Mail 兩種方式
            $sendResult = $this->sendEmailWithMethod(
                'emails.recommendation-invitation',
                [
                    'recommender' => $recommender,
                    'recommendationLetter' => $recommendationLetter,
                    'applicant' => $recommendationLetter->applicant,
                    'loginUrl' => $loginUrl,
                    'deadline' => config('recommendation.deadline', '2024-03-15 23:59'),
                ],
                $recommendationLetter->recommender_email,
                $recommender->name,
                '推薦函邀請 - ' . $recommendationLetter->department_name
            );

            if (!$sendResult['success']) {
                throw new \Exception($sendResult['message']);
            }

            $emailLog->markAsSent();

            return true;
        } catch (\Exception $e) {
            if (isset($emailLog)) {
                $emailLog->markAsFailed($e->getMessage());
            }

            Log::error('Failed to send recommendation invitation', [
                'recommendation_id' => $recommendationLetter->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Send reminder email to recommender.
     */
    public function sendReminder(RecommendationLetter $recommendationLetter): bool
    {
        try {
            // Get recommender
            $recommender = Recommender::where('email', $recommendationLetter->recommender_email)->first();

            if (!$recommender) {
                throw new \Exception('Recommender not found');
            }

            // Generate login URL with token
            $loginUrl = $this->generateLoginUrl($recommender);

            // Create email log
            $emailLog = EmailLog::create([
                'recommendation_letter_id' => $recommendationLetter->id,
                'to_email' => $recommendationLetter->recommender_email,
                'subject' => '推薦函提醒 - ' . $recommendationLetter->department_name,
                'template_name' => 'recommendation-reminder',
                'email_type' => 'reminder',
                'status' => 'pending',
            ]);

            // Send email - 支援外部 API 和 Laravel Mail 兩種方式
            $sendResult = $this->sendEmailWithMethod(
                'emails.recommendation-reminder',
                [
                    'recommender' => $recommender,
                    'recommendationLetter' => $recommendationLetter,
                    'applicant' => $recommendationLetter->applicant,
                    'loginUrl' => $loginUrl,
                    'deadline' => config('recommendation.deadline', '2024-03-15 23:59'),
                    'isReminder' => true,
                ],
                $recommendationLetter->recommender_email,
                $recommender->name,
                '推薦函提醒 - ' . $recommendationLetter->department_name
            );

            if (!$sendResult['success']) {
                throw new \Exception($sendResult['message']);
            }

            $emailLog->markAsSent();

            return true;
        } catch (\Exception $e) {
            if (isset($emailLog)) {
                $emailLog->markAsFailed($e->getMessage());
            }

            Log::error('Failed to send recommendation reminder', [
                'recommendation_id' => $recommendationLetter->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }



    /**
     * 統一的郵件發送方法
     * 根據配置選擇使用外部 API 或 Laravel Mail
     *
     * @param string $template Blade 模板名稱
     * @param array $data 模板資料
     * @param string $recipientEmail 收件人信箱
     * @param string $recipientName 收件人姓名
     * @param string $subject 郵件主旨
     * @return array 發送結果
     */
    private function sendEmailWithMethod(string $template, array $data, string $recipientEmail, string $recipientName, string $subject): array
    {
        if ($this->useExternalApi) {
            // 使用外部 API 發送
            return $this->externalMailService->sendWithTemplate($template, $data, $subject, $recipientEmail);
        } else {
            // 使用 Laravel Mail 發送 (原本的方式，標記為未使用但保留)
            return $this->sendWithLaravelMailMethod($template, $data, $recipientEmail, $recipientName, $subject);
        }
    }

    /**
     * 使用 Laravel Mail 發送郵件 (原本的方式，保留但標記為未使用)
     *
     * @param string $template
     * @param array $data
     * @param string $recipientEmail
     * @param string $recipientName
     * @param string $subject
     * @return array
     */
    private function sendWithLaravelMailMethod(string $template, array $data, string $recipientEmail, string $recipientName, string $subject): array
    {
        try {
            // 根據模板選擇對應的 Mailable 類別
            if (str_contains($template, 'invitation')) {
                $mailable = new RecommendationInvitation(
                    $data['recommendationLetter'],
                    $data['recommender'],
                    $data['loginUrl']
                );
            } elseif (str_contains($template, 'reminder')) {
                $mailable = new RecommendationReminder(
                    $data['recommendationLetter'],
                    $data['recommender'],
                    $data['loginUrl']
                );
            } else {
                throw new \Exception('未支援的郵件模板: ' . $template);
            }

            Mail::to($recipientEmail, $recipientName)->send($mailable);

            return [
                'success' => true,
                'message' => 'Laravel Mail 發送成功'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Laravel Mail 發送失敗: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate login URL with token for recommender.
     */
    private function generateLoginUrl(Recommender $recommender): string
    {
        // If recommender doesn't have a token, generate one
        if (!$recommender->login_token) {
            $recommender->update(['login_token' => Str::random(64)]);
        }

        return route('recommender.auth', [
            'token' => $recommender->login_token,
        ]);
    }

    /**
     * Retry failed emails.
     */
    public function retryFailedEmails(): int
    {
        $failedEmails = EmailLog::where('status', 'failed')
            ->where('retry_count', '<', 3)
            ->with('recommendationLetter')
            ->get();

        $retryCount = 0;

        foreach ($failedEmails as $emailLog) {
            $success = false;

            if ($emailLog->email_type === 'invitation') {
                $success = $this->sendInvitation($emailLog->recommendationLetter);
            } elseif ($emailLog->email_type === 'reminder') {
                $success = $this->sendReminder($emailLog->recommendationLetter);
            }

            if ($success) {
                $retryCount++;
            }
        }

        return $retryCount;
    }

    /**
     * Send automatic reminders for pending recommendations.
     */
    public function sendAutomaticReminders(): int
    {
        // Send reminders for recommendations that are pending for more than 7 days
        $pendingRecommendations = RecommendationLetter::where('status', 'pending')
            ->where('created_at', '<=', now()->subDays(7))
            ->whereDoesntHave('emailLogs', function ($query) {
                $query->where('email_type', 'reminder')
                    ->where('status', 'sent')
                    ->where('sent_at', '>=', now()->subDays(3)); // Don't send if already sent in last 3 days
            })
            ->get();

        $sentCount = 0;

        foreach ($pendingRecommendations as $recommendation) {
            if ($this->sendReminder($recommendation)) {
                $sentCount++;
            }
        }

        return $sentCount;
    }
}
