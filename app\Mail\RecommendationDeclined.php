<?php

namespace App\Mail;

use App\Models\Applicant;
use App\Models\Recommender;
use App\Models\RecommendationLetter;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

/**
 * 推薦函婉拒通知信件
 * 
 * 當推薦人婉拒推薦函邀請時，發送給申請人的通知信件
 */
class RecommendationDeclined extends Mailable
{
    use Queueable, SerializesModels;

    public $applicant;
    public $recommender;
    public $recommendation;
    public $declined_at;

    /**
     * Create a new message instance.
     */
    public function __construct(
        Applicant $applicant,
        Recommender $recommender,
        RecommendationLetter $recommendation,
    ) {
        $this->applicant = $applicant;
        $this->recommender = $recommender;
        $this->recommendation = $recommendation;
        $this->declined_at = now();
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: '推薦函邀請婉拒通知 - ' . $this->recommendation->department_name,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.recommendation-declined',
            with: [
                'applicant' => $this->applicant,
                'recommender' => $this->recommender,
                'recommendation' => $this->recommendation,
                'declined_at' => $this->declined_at,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
