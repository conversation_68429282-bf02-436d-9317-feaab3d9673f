<?php

namespace App\Services;

use App\Models\EmailLog;
use App\Models\RecommendationLetter;
use Illuminate\Support\Facades\Log;

/**
 * 郵件服務類
 *
 * 處理推薦函系統中的郵件發送功能
 * 僅支援外部 API 發送方式
 */
class EmailService
{
    /**
     * 外部郵件服務實例
     */
    private ExternalMailService $externalMailService;

    /**
     * 是否使用外部 API 發送郵件
     */
    private bool $useExternalApi;

    public function __construct()
    {
        $this->externalMailService = new ExternalMailService();
        $this->useExternalApi = config('mail.external_api.enabled', false);
    }
    /**
     * 發送推薦函邀請信
     * 
     * @param RecommendationLetter $recommendationLetter
     * @return bool
     */
    public function sendInvitationEmail(RecommendationLetter $recommendationLetter): bool
    {
        try {
            // 取得推薦人資料
            $recommender = $recommendationLetter->recommender;
            if (!$recommender) {
                throw new \Exception('找不到推薦人資料');
            }

            // 生成登入連結
            $loginUrl = route('recommender.auth', ['token' => $recommender->login_token]);

            // 準備郵件資料
            $emailData = [
                'recommender' => $recommender,
                'recommendationLetter' => $recommendationLetter,
                'applicant' => $recommendationLetter->applicant,
                'loginUrl' => $loginUrl,
                'deadline' => config('recommendation.deadline', '2024-03-15 23:59'),
            ];

            // 建立郵件記錄
            $emailLog = $this->createEmailLog(
                $recommendationLetter,
                $recommender->email,
                $recommender->name,
                EmailLog::TYPE_INVITATION,
                '推薦函邀請 – ' . $recommendationLetter->display_title,
                $emailData
            );

            // 發送郵件 - 支援外部 API 和 Laravel Mail 兩種方式
            $sendResult = $this->sendEmail(
                'emails.recommendation-invitation',
                $emailData,
                $recommender->email,
                $recommender->name,
                '推薦函邀請 – ' . $recommendationLetter->display_title,
            );

            if (!$sendResult['success']) {
                throw new \Exception($sendResult['message']);
            }

            // 標記郵件為已發送
            $emailLog->markAsSent();

            Log::info('推薦函邀請信發送成功', [
                'recommendation_id' => $recommendationLetter->id,
                'recommender_email' => $recommender->email,
                'email_log_id' => $emailLog->id,
            ]);

            return true;
        } catch (\Exception $e) {
            // 記錄錯誤並標記郵件為失敗
            if (isset($emailLog)) {
                $emailLog->markAsFailed($e->getMessage());
            }

            Log::error('推薦函邀請信發送失敗', [
                'recommendation_id' => $recommendationLetter->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 發送推薦函提醒信
     * 
     * @param RecommendationLetter $recommendationLetter
     * @return bool
     */
    public function sendReminderEmail(RecommendationLetter $recommendationLetter): bool
    {
        try {
            // 檢查是否可以發送提醒
            if (!$recommendationLetter->canSendReminder()) {
                throw new \Exception('尚未到達可發送提醒的時間');
            }

            // 取得推薦人資料
            $recommender = $recommendationLetter->recommender;
            if (!$recommender) {
                throw new \Exception('找不到推薦人資料');
            }

            $loginUrl = route('recommender.auth', ['token' => $recommender->login_token]);

            // 準備郵件資料
            $emailData = [
                'recommender' => $recommender,
                'recommendationLetter' => $recommendationLetter,
                'applicant' => $recommendationLetter->applicant,
                'loginUrl' => $loginUrl,
                'deadline' => config('recommendation.deadline', '2024-03-15 23:59'),
                'isReminder' => true,
            ];

            // 建立郵件記錄
            $emailLog = $this->createEmailLog(
                $recommendationLetter,
                $recommender->email,
                $recommender->name,
                EmailLog::TYPE_REMINDER,
                '推薦函提醒 – ' . $recommendationLetter->display_title,
                $emailData
            );

            // 發送郵件 - 支援外部 API 和 Laravel Mail 兩種方式
            $sendResult = $this->sendEmail(
                'emails.recommendation-reminder',
                $emailData,
                $recommender->email,
                $recommender->name,
                '推薦函提醒 – ' . $recommendationLetter->display_title
            );

            if (!$sendResult['success']) {
                throw new \Exception($sendResult['message']);
            }

            // 標記郵件為已發送並更新提醒時間
            $emailLog->markAsSent();
            $recommendationLetter->updateLastReminded();

            Log::info('推薦函提醒信發送成功', [
                'recommendation_id' => $recommendationLetter->id,
                'recommender_email' => $recommender->email,
                'email_log_id' => $emailLog->id,
            ]);

            return true;
        } catch (\Exception $e) {
            // 記錄錯誤並標記郵件為失敗
            if (isset($emailLog)) {
                $emailLog->markAsFailed($e->getMessage());
            }

            Log::error('推薦函提醒信發送失敗', [
                'recommendation_id' => $recommendationLetter->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 發送推薦函提交通知信給申請人
     * 
     * @param RecommendationLetter $recommendationLetter
     * @return bool
     */
    public function sendSubmissionNotificationEmail(RecommendationLetter $recommendationLetter): bool
    {
        try {
            // 取得申請人資料
            $applicant = $recommendationLetter->applicant;
            if (!$applicant || !$applicant->user) {
                throw new \Exception('找不到申請人資料');
            }

            // 準備郵件資料
            $emailData = [
                'applicant' => $applicant,
                'recommendationLetter' => $recommendationLetter,
                'recommender' => $recommendationLetter->recommender,
                'submitted_at' => $recommendationLetter->submitted_at,
            ];

            // 建立郵件記錄
            $emailLog = $this->createEmailLog(
                $recommendationLetter,
                $applicant->user->email,
                $applicant->user->name,
                EmailLog::TYPE_NOTIFICATION,
                '推薦函已提交通知 – ' . $recommendationLetter->display_title,
                $emailData
            );

            // 發送郵件 - 支援外部 API 和 Laravel Mail 兩種方式
            $sendResult = $this->sendEmail(
                'emails.recommendation-submitted',
                $emailData,
                $applicant->user->email,
                $applicant->user->name,
                '推薦函已提交通知 – ' . $recommendationLetter->display_title
            );

            if (!$sendResult['success']) {
                throw new \Exception($sendResult['message']);
            }

            // 標記郵件為已發送
            $emailLog->markAsSent();

            Log::info('推薦函提交通知信發送成功', [
                'recommendation_id' => $recommendationLetter->id,
                'applicant_email' => $applicant->user->email,
                'email_log_id' => $emailLog->id,
            ]);

            return true;
        } catch (\Exception $e) {
            // 記錄錯誤並標記郵件為失敗
            if (isset($emailLog)) {
                $emailLog->markAsFailed($e->getMessage());
            }

            Log::error('推薦函提交通知信發送失敗', [
                'recommendation_id' => $recommendationLetter->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 發送推薦函婉拒通知信給申請人
     * 
     * @param RecommendationLetter $recommendationLetter
     * @return bool
     */
    public function sendDeclineNotificationEmail(RecommendationLetter $recommendationLetter): bool
    {
        try {
            // 取得申請人資料
            $applicant = $recommendationLetter->applicant;
            if (!$applicant || !$applicant->user) {
                throw new \Exception('找不到申請人資料');
            }

            // 取得推薦人資料
            $recommender = $recommendationLetter->recommender;
            if (!$recommender) {
                throw new \Exception('找不到推薦人資料');
            }

            // 準備郵件資料
            $emailData = [
                'applicant' => $applicant,
                'recommendationLetter' => $recommendationLetter,
                'recommender' => $recommender,
            ];

            // 建立郵件記錄
            $emailLog = $this->createEmailLog(
                $recommendationLetter,
                $applicant->user->email,
                $applicant->user->name,
                EmailLog::TYPE_NOTIFICATION,
                '推薦函婉拒通知 – ' . $recommendationLetter->display_title,
                $emailData
            );

            // 發送郵件 - 支援外部 API 和 Laravel Mail 兩種方式
            $sendResult = $this->sendEmail(
                'emails.recommendation-declined',
                $emailData,
                $applicant->user->email,
                $applicant->user->name,
                '推薦函婉拒通知 – ' . $recommendationLetter->display_title
            );

            if (!$sendResult['success']) {
                throw new \Exception($sendResult['message']);
            }
            // 標記郵件為已發送
            $emailLog->markAsSent();

            Log::info('推薦函婉拒通知信發送成功', [
                'recommendation_id' => $recommendationLetter->id,
                'applicant_email' => $applicant->user->email,
                'email_log_id' => $emailLog->id,
            ]);

            return true;
        } catch (\Exception $e) {
            // 記錄錯誤並標記郵件為失敗
            if (isset($emailLog)) {
                $emailLog->markAsFailed($e->getMessage());
            }

            Log::error('推薦函婉拒通知信發送失敗', [
                'recommendation_id' => $recommendationLetter->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 建立郵件記錄
     * 
     * @param RecommendationLetter $recommendationLetter
     * @param string $recipientEmail
     * @param string $recipientName
     * @param string $emailType
     * @param string $subject
     * @param array $metadata
     * @return EmailLog
     */
    private function createEmailLog(
        RecommendationLetter $recommendationLetter,
        string $recipientEmail,
        string $recipientName,
        string $emailType,
        string $subject,
        array $metadata = []
    ): EmailLog {
        return EmailLog::create([
            'recommendation_letter_id' => $recommendationLetter->id,
            'recipient_email' => $recipientEmail,
            'recipient_name' => $recipientName,
            'email_type' => $emailType,
            'subject' => $subject,
            'content' => '', // 實際內容會在郵件模板中生成
            'status' => EmailLog::STATUS_PENDING,
            'metadata' => $metadata,
        ]);
    }

    /**
     * 統一的郵件發送方法
     *
     * @param string $template Blade 模板名稱
     * @param array $data 模板資料
     * @param string $recipientEmail 收件人信箱
     * @param string $subject 郵件主旨
     * @return array 發送結果
     */
    private function sendEmail(string $template, array $data, string $recipientEmail, string $subject): array
    {
        return $this->externalMailService->sendWithTemplate($template, $data, $subject, $recipientEmail);
    }

    /**
     * 重試失敗的郵件
     *
     * @param EmailLog $emailLog
     * @return bool
     */
    public function retryFailedEmail(EmailLog $emailLog): bool
    {
        if (!$emailLog->canRetry()) {
            return false;
        }

        // 根據郵件類型重新發送
        switch ($emailLog->email_type) {
            case EmailLog::TYPE_INVITATION:
                return $this->sendInvitationEmail($emailLog->recommendationLetter);
            case EmailLog::TYPE_REMINDER:
                return $this->sendReminderEmail($emailLog->recommendationLetter);
            case EmailLog::TYPE_NOTIFICATION:
                return $this->sendSubmissionNotificationEmail($emailLog->recommendationLetter);
            default:
                return false;
        }
    }
}
