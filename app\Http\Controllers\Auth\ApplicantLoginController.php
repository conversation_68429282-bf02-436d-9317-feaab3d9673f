<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\AuthenticationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/**
 * 申請人登入控制器
 *
 * 處理申請人透過外部系統 token 的跳轉登入邏輯
 * 使用統一的 AuthenticationService 進行認證處理
 */
class ApplicantLoginController extends Controller
{
  protected AuthenticationService $authService;

  public function __construct(AuthenticationService $authService)
  {
    $this->authService = $authService;
  }

  /**
   * 處理第三方系統 JWT 登入
   *
   * @param Request $request
   * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\Response
   */
  public function handleExternalAuth(Request $request)
  {
    $token = $request->query('token');

    if (!$token) {
      Log::warning('第三方登入失敗: 缺少 token 參數', [
        'ip' => $request->ip(),
        'user_agent' => $request->userAgent(),
      ]);

      return $this->showAuthFailure('登入失敗，請確認您的登入連結是否正確');
    }

    $secretKey = config('app.jwt_secret', 'your_super_secret_key');

    try {
      // 解碼 JWT Token
      $decoded = JWT::decode($token, new Key($secretKey, 'HS256'));

      // 驗證必要欄位
      if (!isset($decoded->stu_idno) || !isset($decoded->stu_year) || !isset($decoded->exam_id)) {
        throw new \Exception('Token 缺少必要資訊');
      }

      $stu_idno = $decoded->stu_idno;
      $stuYear = $decoded->stu_year;
      $examId = $decoded->exam_id;

      Log::info('第三方登入嘗試', [
        'stu_idno' => $stu_idno,
        'stu_year' => $stuYear,
        'exam_id' => $examId,
        'ip' => $request->ip(),
      ]);

      // 準備 JWT payload
      $jwtPayload = [
        'stu_idno' => $stu_idno,
        'stu_year' => $stuYear,
        'exam_id' => $examId,
        'iat' => $decoded->iat ?? null,
        'exp' => $decoded->exp ?? null,
      ];

      // 使用新的 JWT 登入邏輯
      $result = $this->authService->loginApplicantWithJWT($jwtPayload, $request->ip());

      if ($result['success']) {
        return redirect()->route('dashboard')->with('success', '登入成功！歡迎使用推薦函管理系統');
      } else {
        return $this->showAuthFailure($result['message']);
      }
    } catch (\Firebase\JWT\ExpiredException $e) {
      Log::warning('第三方登入失敗: Token 已過期', [
        'token' => substr($token, 0, 20) . '...',
        'error' => $e->getMessage(),
      ]);

      return $this->showAuthFailure('登入失敗，您的登入連結已過期，請重新嘗試');
    } catch (\Firebase\JWT\SignatureInvalidException $e) {
      Log::error('第三方登入失敗: Token 簽名無效', [
        'token' => substr($token, 0, 20) . '...',
        'error' => $e->getMessage(),
      ]);

      return $this->showAuthFailure('登入失敗，請確認您的登入連結是否正確');
    } catch (\Exception $e) {
      Log::error('第三方登入失敗: 未知錯誤', [
        'token' => substr($token, 0, 20) . '...',
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
      ]);

      return $this->showAuthFailure('登入失敗，請稍後再試或聯繫系統管理員');
    }
  }

  /**
   * 顯示認證失敗頁面
   */
  private function showAuthFailure(string $message)
  {
    return inertia('auth/auth-failure', [
      'message' => $message,
      'type' => 'applicant'
    ]);
  }

  /**
   * 處理申請人登出
   * 使用統一認證服務處理登出邏輯
   */
  public function logout()
  {
    $this->authService->logout('applicant');
    return redirect()->route('home')->with('success', '您已成功登出。');
  }
}
