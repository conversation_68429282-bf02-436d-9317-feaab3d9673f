<?php

use App\Http\Controllers\Auth\ApplicantLoginController;
use App\Http\Controllers\Auth\RecommenderAuthController;
use App\Http\Controllers\Api\AdmissionInfoController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API 路由
|--------------------------------------------------------------------------
|
| 這裡定義所有 API 路由，包括第三方系統整合、外部認證等功能
| 注意：Laravel 會自動為 api.php 添加 /api 前綴
|
*/

/**
 * 第三方系統 JWT 登入
 * 外部報名系統透過 JWT Token 進行申請人登入
 *
 * @param string token JWT Token (包含 stu_idno, stu_year, exam_id)
 * @return redirect 成功登入後導向儀表板
 */
Route::get('/auth-from-external', [ApplicantLoginController::class, 'handleExternalAuth'])
    ->middleware(['web'])
    ->name('api.auth.external');
