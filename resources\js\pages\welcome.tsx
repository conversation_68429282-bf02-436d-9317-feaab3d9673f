import AppLogo from '@/components/app-logo';
import { LanguageSwitcher } from '@/components/language-switcher';
import { useLanguage } from '@/hooks/use-language';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { Calendar, Clock } from 'lucide-react';

// 招生資訊介面
interface AdmissionInfo {
    exam_id: string;
    exam_name: string;
    app_date1_start: string;
    app_date1_end: string;
}

// 推薦函系統首頁 - 現代化設計，專為推薦函系統定制
export default function Welcome() {
    const { auth, admission_info } = usePage<SharedData>().props;
    const { t } = useLanguage();

    // 格式化日期時間
    const formatDateTime = (dateTimeStr: string) => {
        try {
            const date = new Date(dateTimeStr.replace(/\//g, '-'));
            return date.toLocaleString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
            });
        } catch {
            return dateTimeStr;
        }
    };

    // 取得招生狀態
    const getAdmissionStatus = (info: AdmissionInfo) => {
        try {
            const now = new Date();
            const startDate = new Date(info.app_date1_start.replace(/\//g, '-'));
            const endDate = new Date(info.app_date1_end.replace(/\//g, '-'));

            if (now < startDate) {
                return { status: 'upcoming', label: '即將到來', color: 'text-blue-600 bg-blue-50' };
            } else if (now > endDate) {
                return { status: 'ended', label: '已結束', color: 'text-gray-600 bg-gray-50' };
            } else {
                return { status: 'active', label: '報名中', color: 'text-green-600 bg-green-50' };
            }
        } catch {
            return { status: 'unknown', label: '狀態未知', color: 'text-gray-600 bg-gray-50' };
        }
    };

    return (
        <>
            <Head title={t('common.appName')}>
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />
            </Head>

            {/* 現代化首頁設計 */}
            <div className="from-green-58 via-whit7 to-green-80 flex min-h-screen flex-col bg-gradient-to-b">
                {/* 導航欄 */}
                <header className="relative z-10 border-b border-gray-200/50 bg-white/80 backdrop-blur-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex h-16 items-center justify-between">
                            {/* Logo 區域 */}
                            <div className="flex items-center space-x-3">
                                <AppLogo />
                            </div>

                            {/* 右側導航 */}
                            <div className="flex items-center space-x-4">
                                <LanguageSwitcher />
                                {auth.user ? (
                                    <Link
                                        href={route('dashboard')}
                                        className="inline-flex items-center rounded-lg bg-green-600 px-4 py-2 text-sm font-medium text-white transition-colors duration-200 hover:bg-green-700"
                                    >
                                        {t('auth.dashboard')}
                                    </Link>
                                ) : (
                                    <div className="flex items-center space-x-3">
                                        <Link
                                            href={route('login')}
                                            className="px-3 py-2 text-sm font-medium text-gray-600 transition-colors duration-200 hover:text-gray-900"
                                        >
                                            {t('auth.login')}
                                        </Link>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </header>

                {/* 主要內容區域 */}
                <main className="flex-1">
                    {/* Hero 區域 */}
                    <div className="relative overflow-hidden border-b border-gray-100 bg-gradient-to-r from-blue-50 to-green-50">
                        <div className="mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8">
                            <div className="text-center">
                                <h1 className="mb-6 text-4xl font-bold text-gray-900 md:text-6xl">
                                    <span className="bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
                                        {t('common.appName')}
                                    </span>
                                </h1>
                                <p className="mx-auto max-w-3xl text-xl leading-relaxed text-gray-600">{t('welcome.welcomeDescription')}</p>
                            </div>
                        </div>

                        {/* 招生狀態訊息區域 */}
                        {admission_info && admission_info.length > 0 && (
                            <div className="mx-auto max-w-7xl px-4 pb-24 sm:px-6 lg:px-8">
                                <div className="text-center">
                                    <h2 className="mb-4 text-2xl font-bold text-gray-900">
                                        <Calendar className="mr-2 inline-block h-6 w-6" />
                                        目前招生資訊
                                    </h2>
                                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
                                        {admission_info.map((info) => {
                                            return (
                                                <div
                                                    key={info.exam_id}
                                                    className={`rounded-lg border p-4 transition-all duration-200 ${
                                                        getAdmissionStatus(info).status === 'active'
                                                            ? 'border-green-200 bg-green-50 hover:shadow-lg'
                                                            : getAdmissionStatus(info).status === 'upcoming'
                                                              ? 'border-blue-200 bg-blue-50 hover:shadow-lg'
                                                              : 'border-gray-200 bg-gray-50 hover:shadow-lg'
                                                    }`}
                                                >
                                                    <h3 className="mb-2 font-semibold text-gray-900">{info.exam_name}</h3>
                                                    <div className="space-y-1 text-sm text-gray-600">
                                                        <div className="flex items-center text-xs">
                                                            <Clock className="mr-1 h-3 w-3" />
                                                            報名時間：{formatDateTime(info.app_date1_start)} ~ {formatDateTime(info.app_date1_end)}
                                                            <div>
                                                                <span
                                                                    className={`ml-2 inline-flex items-center rounded-full px-2 py-1 text-xs font-semibold ${getAdmissionStatus(info).color}`}
                                                                >
                                                                    {getAdmissionStatus(info).label}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </main>

                {/* 頁尾 */}
                <footer className="border-t border-gray-200 bg-gray-50">
                    <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <p className="text-gray-600">© 2025 National United University. All rights reserved。</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
